<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上海意拉罐科技有限公司 - 技术服务与软件开发专家</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        /* 导航栏 */
        .navbar {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 0 50px;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            height: 70px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2c5aa0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover,
        .nav-menu a.current {
            color: #2c5aa0;
        }

        /* 主横幅 */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 150px 20px 100px;
            margin-top: 70px;
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .hero h2 {
            font-size: 24px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .hero p {
            font-size: 18px;
            max-width: 600px;
            margin: 0 auto 40px;
            opacity: 0.8;
        }

        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 16px;
            transition: background 0.3s;
        }

        .cta-button:hover {
            background: #ff5252;
        }

        /* 客户案例 */
        .clients {
            padding: 80px 20px;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 36px;
            color: #333;
            margin-bottom: 15px;
        }

        .section-title p {
            font-size: 18px;
            color: #666;
        }

        .client-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }

        .client-item {
            background: white;
            padding: 30px 20px;
            text-align: center;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .client-item:hover {
            transform: translateY(-5px);
        }

        .client-item h3 {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
        }

        .client-item p {
            color: #666;
            font-size: 14px;
        }

        /* 公司信息 */
        .company-info {
            padding: 80px 20px;
            background: white;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            border-left: 4px solid #2c5aa0;
        }

        .info-card h3 {
            color: #2c5aa0;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .info-card p {
            color: #666;
            line-height: 1.8;
        }

        /* 联系我们 */
        .contact {
            padding: 80px 20px;
            background: #2c5aa0;
            color: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .contact-item h3 {
            margin-bottom: 20px;
            font-size: 24px;
        }

        .contact-item p {
            margin-bottom: 10px;
            opacity: 0.9;
        }

        /* 页脚 */
        .footer {
            background: #1a1a1a;
            color: white;
            text-align: center;
            padding: 30px 20px;
        }

        .footer p {
            opacity: 0.7;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar {
                padding: 0 20px;
            }

            .nav-menu {
                gap: 15px;
            }

            .hero h1 {
                font-size: 32px;
            }

            .hero h2 {
                font-size: 20px;
            }

            .section-title h2 {
                font-size: 28px;
            }

            .client-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">意拉罐科技</div>
            <ul class="nav-menu">
                <li><a href="#" class="current">首页</a></li>
                <li><a href="#clients">客户案例</a></li>
                <li><a href="#about">关于我们</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主横幅 -->
    <section class="hero">
        <h1>技术赋能，创新未来</h1>
        <h2>TECHNOLOGY EMPOWERS INNOVATION</h2>
        <p>专注于技术服务、软件开发、系统集成，为企业数字化转型提供全方位解决方案</p>
        <a href="#contact" class="cta-button">了解更多</a>
    </section>

    <!-- 客户案例 -->
    <section class="clients" id="clients">
        <div class="container">
            <div class="section-title">
                <h2>客户案例</h2>
                <p>行业覆盖电子制造、新能源、汽车、建筑设计、农业、互联网等多个领域</p>
            </div>
            <div class="client-grid">
                <div class="client-item">
                    <h3>华硕</h3>
                    <p>计算机硬件制造</p>
                </div>
                <div class="client-item">
                    <h3>佳世达</h3>
                    <p>电子设备解决方案</p>
                </div>
                <div class="client-item">
                    <h3>泉峰集团</h3>
                    <p>电动工具制造</p>
                </div>
                <div class="client-item">
                    <h3>宝时得科技</h3>
                    <p>园林工具技术服务</p>
                </div>
                <div class="client-item">
                    <h3>阿特斯太阳能</h3>
                    <p>新能源技术支持</p>
                </div>
                <div class="client-item">
                    <h3>科沃斯</h3>
                    <p>智能家居系统</p>
                </div>
                <div class="client-item">
                    <h3>宝丽迪</h3>
                    <p>塑料制品技术</p>
                </div>
                <div class="client-item">
                    <h3>丰盛集团</h3>
                    <p>综合企业服务</p>
                </div>
                <div class="client-item">
                    <h3>未来电器</h3>
                    <p>电器设备技术</p>
                </div>
                <div class="client-item">
                    <h3>长安马自达</h3>
                    <p>汽车制造业</p>
                </div>
                <div class="client-item">
                    <h3>中衡设计</h3>
                    <p>建筑设计服务</p>
                </div>
                <div class="client-item">
                    <h3>勤川农业</h3>
                    <p>农业技术服务</p>
                </div>
                <div class="client-item">
                    <h3>罗杰斯</h3>
                    <p>材料技术解决方案</p>
                </div>
                <div class="client-item">
                    <h3>康迈</h3>
                    <p>工业设备服务</p>
                </div>
                <div class="client-item">
                    <h3>边锋网络</h3>
                    <p>互联网技术服务</p>
                </div>
                <div class="client-item">
                    <h3>叠纸网络</h3>
                    <p>游戏开发技术</p>
                </div>
            </div>
            <div style="text-align: center;">
                <p style="color: #666;">更多客户案例，请联系我们了解详情</p>
            </div>
        </div>
    </section>

    <!-- 公司信息 -->
    <section class="company-info" id="about">
        <div class="container">
            <div class="section-title">
                <h2>关于我们</h2>
                <p>上海意拉罐科技有限公司 - 您值得信赖的技术合作伙伴</p>
            </div>
            <div class="info-grid">
                <div class="info-card">
                    <h3>公司概况</h3>
                    <p><strong>公司名称：</strong>上海意拉罐科技有限公司<br>
                    <strong>成立时间：</strong>2025年06月16日<br>
                    <strong>注册资本：</strong>100万元人民币<br>
                    <strong>法定代表人：</strong>郁水英<br>
                    <strong>企业类型：</strong>有限责任公司</p>
                </div>
                <div class="info-card">
                    <h3>核心业务</h3>
                    <p>专注于技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；软件开发；计算机系统服务；信息系统集成服务；智能控制系统集成；信息技术咨询服务；信息系统运行维护服务；网络技术服务等。</p>
                </div>
                <div class="info-card">
                    <h3>服务优势</h3>
                    <p>拥有专业的技术团队，丰富的项目经验，为客户提供从咨询、设计、开发到运维的全生命周期服务。我们致力于通过技术创新，帮助企业实现数字化转型和智能化升级。</p>
                </div>
                <div class="info-card">
                    <h3>发展愿景</h3>
                    <p>成为行业领先的技术服务提供商，通过持续的技术创新和优质的服务，为客户创造更大价值，推动行业数字化发展，共建智能化未来。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact" id="contact">
        <div class="container">
            <div class="section-title" style="color: white;">
                <h2>联系我们</h2>
                <p style="color: rgba(255,255,255,0.8);">期待与您的合作</p>
            </div>
            <div class="contact-grid">
                <div class="contact-item">
                    <h3>上海总部</h3>
                    <p><strong>公司名称：</strong>上海意拉罐科技有限公司</p>
                    <p><strong>注册地址：</strong>上海市嘉定区浏翔公路955号7幢22层A区-01J</p>
                    <p><strong>邮政编码：</strong>201800</p>
                </div>
                <div class="contact-item">
                    <h3>联系方式</h3>
                    <p><strong>业务咨询：</strong><EMAIL></p>
                    <p><strong>技术支持：</strong><EMAIL></p>
                    <p><strong>人才招聘：</strong><EMAIL></p>
                    <p><strong>客服热线：</strong>400-888-0000</p>
                </div>
                <div class="contact-item">
                    <h3>服务时间</h3>
                    <p><strong>工作日：</strong>周一至周五 9:00-18:00</p>
                    <p><strong>客服热线：</strong>7×24小时服务</p>
                    <p><strong>技术支持：</strong>7×24小时在线</p>
                    <p><strong>紧急响应：</strong>1小时内响应</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 上海意拉罐科技有限公司 版权所有</p>
            <p>统一社会信用代码：91310114MAENRW7W4R | 沪ICP备2025000000号-1</p>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.backdropFilter = 'blur(10px)';
            } else {
                navbar.style.background = '#fff';
                navbar.style.backdropFilter = 'none';
            }
        });

        // 客户案例动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.client-item, .info-card').forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(item);
        });

        // 添加页面加载动画
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
