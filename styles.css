/* 额外的样式增强 */

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #2c5aa0;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e3d6f;
}

/* 按钮悬停效果增强 */
.cta-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.cta-button:hover::before {
    left: 100%;
}

/* 客户案例卡片增强效果 */
.client-item {
    position: relative;
    overflow: hidden;
}

.client-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #2c5aa0, #667eea);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.client-item:hover::before {
    transform: scaleX(1);
}

/* 信息卡片动画 */
.info-card {
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(44, 90, 160, 0.1);
}

/* 导航菜单移动端优化 */
@media (max-width: 768px) {
    .nav-menu {
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: none;
        padding: 20px 0;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-container {
        position: relative;
    }

    .menu-toggle {
        display: block;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #333;
    }

    .menu-toggle:before {
        content: '☰';
    }

    .menu-toggle.active:before {
        content: '✕';
    }
}

/* 加载动画 */
.loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* 渐变文字效果 */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 淡入动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease forwards;
}

/* 联系卡片特殊效果 */
.contact-item {
    position: relative;
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-3px);
}

.contact-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #ff6b6b, #ffa500);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.contact-item:hover::after {
    transform: scaleX(1);
}

/* 响应式图片 */
img {
    max-width: 100%;
    height: auto;
}

/* 表单样式（如果需要添加表单） */
.form-group {
    margin-bottom: 20px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

/* 成功消息样式 */
.success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #c3e6cb;
    margin: 20px 0;
}

/* 错误消息样式 */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #f5c6cb;
    margin: 20px 0;
}

/* 工具提示样式 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 打印样式 */
@media print {
    .navbar,
    .cta-button,
    .footer {
        display: none;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .hero {
        background: none !important;
        color: #000 !important;
    }
}
